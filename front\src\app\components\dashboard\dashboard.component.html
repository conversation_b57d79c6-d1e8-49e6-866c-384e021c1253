<div class="dashboard-container">
  <header class="dashboard-header">
    <div class="header-content">
      <h1 class="dashboard-title">Tableau de bord Paximum</h1>
      <div class="user-info" *ngIf="userInfo">
        <span class="welcome-text">Bienvenue, {{ userInfo.name }}</span>
        <button class="logout-button" (click)="logout()">
          <svg class="logout-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Déconnexion
        </button>
      </div>
    </div>
  </header>

  <main class="dashboard-main">
    <div class="dashboard-content">
      <div class="info-card" *ngIf="userInfo">
        <h2>Informations utilisateur</h2>
        <div class="info-grid">
          <div class="info-item">
            <label>Code utilisateur :</label>
            <span>{{ userInfo.code }}</span>
          </div>
          <div class="info-item">
            <label>Nom :</label>
            <span>{{ userInfo.name }}</span>
          </div>
          <div class="info-item">
            <label>Agence :</label>
            <span>{{ userInfo.agency?.name }} ({{ userInfo.agency?.code }})</span>
          </div>
          <div class="info-item">
            <label>Bureau :</label>
            <span>{{ userInfo.office?.name }} ({{ userInfo.office?.code }})</span>
          </div>
          <div class="info-item">
            <label>Opérateur :</label>
            <span>{{ userInfo.operator?.name }} ({{ userInfo.operator?.code }})</span>
          </div>
          <div class="info-item">
            <label>Marché :</label>
            <span>{{ userInfo.market?.name }} ({{ userInfo.market?.code }})</span>
          </div>
        </div>
      </div>

      <div class="success-message">
        <svg class="success-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <h3>Connexion réussie !</h3>
        <p>Vous êtes maintenant connecté à votre compte Paximum.</p>
      </div>
    </div>
  </main>
</div>
